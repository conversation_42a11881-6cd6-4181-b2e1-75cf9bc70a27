package com.eversec.ebp.serverless.gateway.manager.business.strategy.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 访问控制_黑白名单
 * @TableName api_access_restrict
 */
@TableName(value ="api_access_restrict")
@Data
public class ApiAccessRestrict {
    /**
     * 
     */
    @TableId(value = "id")
    private String id;

    /**
     * 限制类型1:IP
     */
    @TableField(value = "restriction_type")
    private Integer restriction_type;

    /**
     * 动作：允许or拒绝
     */
    @TableField(value = "action")
    private String action;

    /**
     * 一个ip或一个IP段
     */
    @TableField(value = "content")
    private String content;

}