package com.eversec.ebp.serverless.gateway.manager.business.strategy.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.eversec.ebp.serverless.gateway.manager.business.strategy.domain.ApiAccessRestrict;

/**
* <AUTHOR>
* @description 针对表【api_access_restrict(访问控制_黑白名单)】的数据库操作Mapper
* @createDate 2025-05-22 16:01:24
* @Entity generator.domain.ApiAccessRestrict
*/
public interface ApiAccessRestrictMapper extends BaseMapper<ApiAccessRestrict> {

}




