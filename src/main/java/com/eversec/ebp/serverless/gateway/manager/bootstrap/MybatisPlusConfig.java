package com.eversec.ebp.serverless.gateway.manager.bootstrap;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.type.LocalDateTimeTypeHandler;
import org.apache.ibatis.type.LocalDateTypeHandler;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Configuration
@MapperScan("com.eversec.ebp.serverless.gateway.manager.business.*.mapper") // 指定 Mapper 接口扫描路径
public class MybatisPlusConfig {

	/**
	 * MyBatis-Plus 拦截器配置
	 */
	@Bean
	public MybatisPlusInterceptor mybatisPlusInterceptor() {
		MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
		// 1. 分页插件配置
		PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
		paginationInterceptor.setOverflow(true); // 页码溢出处理
		paginationInterceptor.setMaxLimit(1000L); // 单页最大记录数限制
		interceptor.addInnerInterceptor(paginationInterceptor);
		// 2. 乐观锁插件（需配合实体类的 @Version 注解使用）
		interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
		return interceptor;
	}

	/**
	 * 自动填充配置（处理 createTime、updateTime 等字段）
	 */
	@Bean
	public MetaObjectHandler metaObjectHandler() {
		return new MetaObjectHandler() {
			@Override
			public void insertFill(MetaObject metaObject) {
				this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
				this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
				this.strictInsertFill(metaObject, "deleted", Integer.class, 0); // 逻辑删除默认值
			}

			@Override
			public void updateFill(MetaObject metaObject) {
				this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
			}
		};
	}

	/**
	 * 自定义类型处理器（可选）
	 */
	@Bean
	public ConfigurationCustomizer configurationCustomizer() {
		return configuration -> {
			// 注册自定义类型处理器
			configuration.getTypeHandlerRegistry().register(LocalDateTime.class, new LocalDateTimeTypeHandler());
			configuration.getTypeHandlerRegistry().register(LocalDate.class, new LocalDateTypeHandler());
		};
	}

}