package com.eversec.ebp.serverless.gateway.manager.business.strategy.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.manager.business.strategy.domain.ApiAccess;
import com.eversec.ebp.serverless.gateway.manager.business.strategy.mapper.ApiAccessMapper;
import com.eversec.ebp.serverless.gateway.manager.business.strategy.service.ApiAccessService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【api_access(访问控制表)】的数据库操作Service实现
* @createDate 2025-05-22 16:01:24
*/
@Service
public class ApiAccessServiceImpl extends ServiceImpl<ApiAccessMapper, ApiAccess>
    implements ApiAccessService {

}




