package com.eversec.ebp.serverless.gateway.manager;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@EnableScheduling
@EnableSwagger2
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages="com.eversec.ebp.system")
@ComponentScan({"com.eversec.ebp.system", "com.eversec.ebp.serverless"})
public class GatewayManagerApp {
	
	private static ApplicationContext context;
	
	public static void main(String[] args) {
		context = SpringApplication.run(GatewayManagerApp.class, args);
	}
	
	public static ApplicationContext context() {
		return context;
	}
	
}
