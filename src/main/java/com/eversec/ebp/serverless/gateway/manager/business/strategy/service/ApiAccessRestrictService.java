package com.eversec.ebp.serverless.gateway.manager.business.strategy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eversec.ebp.serverless.gateway.manager.business.strategy.domain.ApiAccessRestrict;

/**
* <AUTHOR>
* @description 针对表【api_access_restrict(访问控制_黑白名单)】的数据库操作Service
* @createDate 2025-05-22 16:01:24
*/
public interface ApiAccessRestrictService extends IService<ApiAccessRestrict> {

}
