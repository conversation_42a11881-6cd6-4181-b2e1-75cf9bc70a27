package com.eversec.ebp.serverless.gateway.manager.business.strategy.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eversec.ebp.serverless.gateway.manager.business.strategy.domain.ApiAccessRestrict;
import com.eversec.ebp.serverless.gateway.manager.business.strategy.mapper.ApiAccessRestrictMapper;
import com.eversec.ebp.serverless.gateway.manager.business.strategy.service.ApiAccessRestrictService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【api_access_restrict(访问控制_黑白名单)】的数据库操作Service实现
* @createDate 2025-05-22 16:01:24
*/
@Service
public class ApiAccessRestrictServiceImpl extends ServiceImpl<ApiAccessRestrictMapper, ApiAccessRestrict>
    implements ApiAccessRestrictService {

}




