package generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import generator.domain.ApiAccessRestrict;
import generator.service.ApiAccessRestrictService;
import generator.mapper.ApiAccessRestrictMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【api_access_restrict(访问控制_黑白名单)】的数据库操作Service实现
* @createDate 2025-05-22 16:01:24
*/
@Service
public class ApiAccessRestrictServiceImpl extends ServiceImpl<ApiAccessRestrictMapper, ApiAccessRestrict>
    implements ApiAccessRestrictService{

}




