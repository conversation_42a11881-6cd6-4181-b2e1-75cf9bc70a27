package generator.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 访问控制表
 * @TableName api_access
 */
@TableName(value ="api_access")
@Data
public class ApiAccess {
    /**
     * 
     */
    @TableId(value = "id")
    private String id;

    /**
     * 策略名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 支持跨域
     */
    @TableField(value = "is_cross_domain")
    private Boolean is_cross_domain;

    /**
     * 是否开启user-Agent
     */
    @TableField(value = "is_user_agent")
    private Boolean is_user_agent;

    /**
     * 指定user-agent限制：
     */
    @TableField(value = "user_agent")
    private String user_agent;

    /**
     * 允许访问的开始时间
     */
    @TableField(value = "start_time")
    private String start_time;

    /**
     * 允许访问的截止时间
     */
    @TableField(value = "end_time")
    private String end_time;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String create_user;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Long create_time;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String update_user;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Long update_time;
}