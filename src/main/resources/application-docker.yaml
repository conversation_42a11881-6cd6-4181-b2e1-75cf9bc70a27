#eureka
eureka:
  client: 
    service-url:
      defaultZone: http://${EUREKA_HOST}:${EUREKA_PORT}/eureka/

spring:
  redis:
    database: ${REDIS_DB}
    host: ${REDIS_HOST}
    password: ${REDIS_PASSWORD}
    port: ${REDIS_PORT}

  datasource:
    username: ${DATABASE_USER}
    password: ${DATABASE_PASSWORD}
    url: jdbc:mysql://${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}?serverTimezone=Asia/Shanghai&Unicode=true&Unicode=true&characterEncoding=UTF-8
