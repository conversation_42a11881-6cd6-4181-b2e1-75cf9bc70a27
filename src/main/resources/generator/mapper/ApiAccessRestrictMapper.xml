<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.ApiAccessRestrictMapper">

    <resultMap id="BaseResultMap" type="generator.domain.ApiAccessRestrict">
            <id property="id" column="id" />
            <result property="restriction_type" column="restriction_type" />
            <result property="action" column="action" />
            <result property="content" column="content" />
    </resultMap>

    <sql id="Base_Column_List">
        id,restriction_type,action,content
    </sql>
</mapper>
