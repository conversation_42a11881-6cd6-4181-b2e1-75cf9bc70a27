<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generator.mapper.ApiAccessMapper">

    <resultMap id="BaseResultMap" type="generator.domain.ApiAccess">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="is_cross_domain" column="is_cross_domain" />
            <result property="is_user_agent" column="is_user_agent" />
            <result property="user_agent" column="user_agent" />
            <result property="start_time" column="start_time" />
            <result property="end_time" column="end_time" />
            <result property="create_user" column="create_user" />
            <result property="create_time" column="create_time" />
            <result property="update_user" column="update_user" />
            <result property="update_time" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,is_cross_domain,is_user_agent,user_agent,start_time,
        end_time,create_user,create_time,update_user,update_time
    </sql>
</mapper>
